import FrontLayout from '@/layouts/front-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
// import { Link } from '@inertiajs/react';
// import { Calendar, Clock, User } from 'lucide-react';
import { FileText, Tag, FileDown, FileType, LucideFileText } from 'lucide-react';
// import { useState } from 'react';

export default function Dossiers() {
  // État pour suivre la catégorie sélectionnée
//   const [selectedCategory, setSelectedCategory] = useState("Tous");

  // Exemple d'articles
//   const articles = [
//     {
//       id: 1,
//       title: "Les nouvelles règles du permis à points en 2025",
//       excerpt: "Découvrez les changements importants concernant le permis à points qui entreront en vigueur en 2025.",
//       date: "15 avril 2025",
//       author: "<PERSON>",
//       category: "Législation",
//       image: "/images/article1.jpg"
//     },
//     {
//       id: 2,
//       title: "Comment bien préparer son Stages de sensibilisation à la sécurité routière",
//       excerpt: "Conseils pratiques pour tirer le meilleur parti de votre stage de sensibilisation à la sécurité routière.",
//       date: "10 avril 2025",
//       author: "Sophie Durand",
//       category: "Conseils",
//       image: "/images/article2.jpg"
//     },
//     {
//       id: 3,
//       title: "Les effets du cannabis sur la conduite",
//       excerpt: "Étude scientifique sur les impacts du cannabis sur les capacités de conduite et les risques associés.",
//       date: "5 avril 2025",
//       author: "Dr. Thomas Petit",
//       category: "Santé",
//       image: "/images/article3.jpg"
//     },
//     {
//       id: 4,
//       title: "Téléphone au volant : les sanctions se durcissent",
//       excerpt: "Le gouvernement renforce les sanctions contre l'usage du téléphone pendant la conduite. Quels sont les changements ?",
//       date: "1 avril 2025",
//       author: "Julie Moreau",
//       category: "Actualités",
//       image: "/images/article4.jpg"
//     },
//     {
//       id: 5,
//       title: "Comprendre les tests psychotechniques",
//       excerpt: "Tout ce que vous devez savoir sur les tests psychotechniques : déroulement, épreuves, préparation.",
//       date: "25 mars 2025",
//       author: "Marc Dubois",
//       category: "Informations",
//       image: "/images/article5.jpg"
//     },
//     {
//       id: 6,
//       title: "La santé mentale des conducteurs professionnels",
//       excerpt: "Enquête sur les enjeux de santé mentale chez les conducteurs professionnels et les solutions proposées.",
//       date: "20 mars 2025",
//       author: "Émilie Blanc",
//       category: "PSSM",
//       image: "/images/article6.jpg"
//     }
//   ];

  // Documents à télécharger
  const documents = [
    {
      id: 101,
      title: "Demande de remboursement PC Probatoire",
      description: "Formulaire officiel pour demander le remboursement de votre permis probatoire",
      category: "Documents",
      fileType: "pdf",
      fileUrl: "/documents/Demande-de-remboursement-PC-Probatoire.pdf",
      iconUrl: "/images/PDF.png"
    },
    {
      id: 102,
      title: "Fiche d'inscription",
      description: "Formulaire d'inscription à remplir pour les stages de récupération de points",
      category: "Documents",
      fileType: "docx",
      fileUrl: "/documents/FICHE-INSCRIPTION.docx",
      iconUrl: "/images/Word.png"
    }
  ];

  // Catégories pour le filtrage (ajout de "Documents")
//   const categories = ["Tous", "Législation", "Conseils", "Santé", "Actualités", "Informations", "PSSM", "Documents"];

  // Filtrer les articles en fonction de la catégorie sélectionnée
//   const filteredItems = selectedCategory === "Tous"
//     ? [...articles, ...documents]
//     : selectedCategory === "Documents"
//       ? documents
//       : articles.filter(article => article.category === selectedCategory);

  return (
    <FrontLayout title="Dossiers">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-3 mb-6">
          <FileText className="h-8 w-8" />
          <h1 className="text-3xl font-bold">Dossiers et articles</h1>
        </div>

        {/* <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={category === selectedCategory ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card> */}

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
          {documents.map((item) => (
            'fileType' in item ? (
              // Rendu pour les documents
              <Card key={item.id} className="overflow-hidden flex flex-col">
                <div className="aspect-video bg-muted relative flex items-center justify-center">
                  {'fileType' in item && item.fileType === 'pdf' ? (
                    <LucideFileText className="h-16 w-16 text-red-500" />
                  ) : (
                    <FileType className="h-16 w-16 text-blue-500" />
                  )}
                </div>
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">{item.category}</span>
                  </div>
                  <CardTitle className="line-clamp-2">{item.title}</CardTitle>
                </CardHeader>
                <CardContent className="flex-1">
                  <p className="line-clamp-3 text-muted-foreground mb-4">
                    {item.description}
                  </p>
                </CardContent>
                <div className="px-6 pb-6">
                  <Button variant="outline" className="w-full" asChild>
                    <a href={item.fileUrl} target="_blank" rel="noopener noreferrer">
                      <FileDown className="mr-2 h-4 w-4" />
                      Télécharger
                    </a>
                  </Button>
                </div>
              </Card>
            ) : (
                <></>
              // Rendu pour les articles
            //   <Card key={item.id} className="overflow-hidden flex flex-col">
            //     <div className="aspect-video bg-muted relative">
            //       {/* Placeholder pour l'image */}
            //       <div className="absolute inset-0 flex items-center justify-center bg-muted">
            //         <span className="text-muted-foreground">Image de l'article</span>
            //       </div>
            //     </div>
            //     <CardHeader>
            //       <div className="flex items-center gap-2 mb-2">
            //         <Tag className="h-4 w-4 text-muted-foreground" />
            //         <span className="text-xs text-muted-foreground">{item.category}</span>
            //       </div>
            //       <CardTitle className="line-clamp-2">{item.title}</CardTitle>
            //     </CardHeader>
            //     <CardContent className="flex-1">
            //       <p className="line-clamp-3 text-muted-foreground mb-4">
            //         {item.excerpt}
            //       </p>
            //       <div className="flex items-center gap-4 text-xs text-muted-foreground">
            //         <div className="flex items-center gap-1">
            //           <Calendar className="h-3 w-3" />
            //           <span>{item.date}</span>
            //         </div>
            //         <div className="flex items-center gap-1">
            //           <User className="h-3 w-3" />
            //           <span>{item.author}</span>
            //         </div>
            //         <div className="flex items-center gap-1">
            //           <Clock className="h-3 w-3" />
            //           <span>5 min</span>
            //         </div>
            //       </div>
            //     </CardContent>
            //     <div className="px-6 pb-6">
            //       <Button variant="outline" className="w-full" asChild>
            //         <Link href={`/dossiers/${item.id}`}>Lire l'article</Link>
            //       </Button>
            //     </div>
            //   </Card>
            )
          ))}
        </div>

        {/* <div className="flex justify-center">
          <div className="flex gap-2">
            <Button variant="outline" size="icon" disabled>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </Button>
            <Button variant="outline" size="sm">1</Button>
            <Button variant="outline" size="sm">2</Button>
            <Button variant="outline" size="sm">3</Button>
            <Button variant="outline" size="icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </Button>
          </div>
        </div> */}

        <Card className="mt-12">
          <CardHeader>
            <CardTitle>Abonnez-vous à notre newsletter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <input
                type="email"
                placeholder="Votre adresse email"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
              <Button>S'abonner</Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              En vous abonnant, vous acceptez de recevoir nos derniers articles et informations.
              Vous pourrez vous désabonner à tout moment.
            </p>
          </CardContent>
        </Card>
      </div>
    </FrontLayout>
  );
}

