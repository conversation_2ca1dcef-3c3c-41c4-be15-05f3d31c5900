import { Head, router } from '@inertiajs/react';
import { Reservation, BreadcrumbItem } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowLeft, Calendar, CreditCard, Hash, MapPin, User, FileText, Phone, Mail, Home } from 'lucide-react';

interface ShowProps {
    reservation: Reservation;
}

export default function Show({ reservation }: ShowProps) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'confirmée':
                return <Badge className="bg-green-500">Confirmée</Badge>;
            case 'en attente':
                return <Badge className="bg-yellow-500">En attente</Badge>;
            case 'annulée':
                return <Badge className="bg-red-500">Annulée</Badge>;
            default:
                return <Badge>{status}</Badge>;
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Réservations',
            href: '/admin/reservations',
        },
        {
            title: `Réservation #${reservation.id}`,
            href: `/admin/reservations/${reservation.id}`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Réservation #${reservation.id}`} />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="flex items-center gap-4 mb-6">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.visit(route('admin.reservations.index'))}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Retour aux réservations
                    </Button>
                    <div className="flex-1">
                        <h1 className="text-2xl font-bold">Détails de la réservation #{reservation.id}</h1>
                        <div className="flex items-center gap-2 mt-1">
                            {getStatusBadge(reservation.statut)}
                            <span className="text-muted-foreground">
                                Réservée le {format(parseISO(reservation.date_reservation), 'dd/MM/yyyy à HH:mm', { locale: fr })}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informations du client */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Informations du client
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h4 className="font-medium text-sm text-muted-foreground">Nom complet</h4>
                                <p className="text-lg font-medium">{reservation.user?.prenom} {reservation.user?.nom}</p>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
                                        <Mail className="h-3 w-3" />
                                        Email
                                    </h4>
                                    <p>{reservation.user?.email}</p>
                                </div>
                                <div>
                                    <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
                                        <Phone className="h-3 w-3" />
                                        Téléphone
                                    </h4>
                                    <p>{reservation.user?.mobile || 'Non renseigné'}</p>
                                </div>
                            </div>
                            {(reservation.user?.adresse || reservation.user?.ville || reservation.user?.code_postal) && (
                                <div>
                                    <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
                                        <Home className="h-3 w-3" />
                                        Adresse
                                    </h4>
                                    <div className="text-sm">
                                        {reservation.user?.adresse && <p>{reservation.user.adresse}</p>}
                                        {(reservation.user?.code_postal || reservation.user?.ville) && (
                                            <p>{reservation.user?.code_postal} {reservation.user?.ville}</p>
                                        )}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Informations du stage */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Informations du stage
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h4 className="font-medium text-sm text-muted-foreground">Référence</h4>
                                <p className="text-lg font-medium">{reservation.stage?.reference}</p>
                            </div>
                            <div>
                                <h4 className="font-medium text-sm text-muted-foreground">Type de stage</h4>
                                <p>{reservation.type_stage?.nom}</p>
                                {reservation.type_stage?.description && (
                                    <p className="text-sm text-muted-foreground mt-1">{reservation.type_stage.description}</p>
                                )}
                            </div>
                            <div>
                                <h4 className="font-medium text-sm text-muted-foreground">Dates</h4>
                                <p>Du {format(parseISO(reservation.stage?.date_debut || ''), 'dd/MM/yyyy', { locale: fr })}</p>
                                <p>Au {format(parseISO(reservation.stage?.date_fin || ''), 'dd/MM/yyyy', { locale: fr })}</p>
                            </div>
                            <div>
                                <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
                                    <MapPin className="h-3 w-3" />
                                    Lieu
                                </h4>
                                <div>
                                    <p className="font-medium">{reservation.stage?.lieu?.nom}</p>
                                    {reservation.stage?.lieu?.adresse && <p className="text-sm">{reservation.stage.lieu.adresse}</p>}
                                    <p className="text-sm text-muted-foreground">
                                        {reservation.stage?.lieu?.ville?.nom} ({reservation.stage?.lieu?.ville?.departement?.code})
                                    </p>
                                </div>
                            </div>
                            <div>
                                <h4 className="font-medium text-sm text-muted-foreground">Prix</h4>
                                <p className="text-lg font-bold">{reservation.stage?.prix} €</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Informations de paiement */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="h-5 w-5" />
                                Informations de paiement
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {reservation.methode_paiement || reservation.date_paiement || reservation.transaction_id ? (
                                <>
                                    {reservation.methode_paiement && (
                                        <div>
                                            <h4 className="font-medium text-sm text-muted-foreground">Méthode de paiement</h4>
                                            <p>{reservation.methode_paiement}</p>
                                        </div>
                                    )}
                                    {reservation.date_paiement && (
                                        <div>
                                            <h4 className="font-medium text-sm text-muted-foreground">Date de paiement</h4>
                                            <p>{format(parseISO(reservation.date_paiement), 'dd/MM/yyyy à HH:mm', { locale: fr })}</p>
                                        </div>
                                    )}
                                    {reservation.transaction_id && (
                                        <div>
                                            <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-1">
                                                <Hash className="h-3 w-3" />
                                                ID de transaction
                                            </h4>
                                            <p className="font-mono text-sm">{reservation.transaction_id}</p>
                                        </div>
                                    )}
                                </>
                            ) : (
                                <p className="text-muted-foreground">Aucune information de paiement disponible</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Informations sur l'infraction */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Informations sur l'infraction
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {reservation.date_infraction || reservation.heure_infraction || reservation.lieu_infraction ? (
                                <>
                                    {reservation.date_infraction && (
                                        <div>
                                            <h4 className="font-medium text-sm text-muted-foreground">Date de l'infraction</h4>
                                            <p>{format(parseISO(reservation.date_infraction), 'dd/MM/yyyy', { locale: fr })}</p>
                                        </div>
                                    )}
                                    {reservation.heure_infraction && (
                                        <div>
                                            <h4 className="font-medium text-sm text-muted-foreground">Heure de l'infraction</h4>
                                            <p>{reservation.heure_infraction}</p>
                                        </div>
                                    )}
                                    {reservation.lieu_infraction && (
                                        <div>
                                            <h4 className="font-medium text-sm text-muted-foreground">Lieu de l'infraction</h4>
                                            <p>{reservation.lieu_infraction}</p>
                                        </div>
                                    )}
                                </>
                            ) : (
                                <p className="text-muted-foreground">Aucune information sur l'infraction disponible</p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Documents */}
                {(reservation.permis_recto || reservation.permis_verso || reservation.lettre_48n_recto || reservation.lettre_48n_verso) && (
                    <Card className="mt-6">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Documents téléchargés
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                {reservation.permis_recto && (
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-2">Permis (recto)</h4>
                                        <Button variant="outline" size="sm" asChild className="w-full">
                                            <a href={`/files/${reservation.permis_recto}`} target="_blank">
                                                <FileText className="h-4 w-4 mr-2" />
                                                Télécharger
                                            </a>
                                        </Button>
                                    </div>
                                )}
                                {reservation.permis_verso && (
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-2">Permis (verso)</h4>
                                        <Button variant="outline" size="sm" asChild className="w-full">
                                            <a href={`/files/${reservation.permis_verso}`} target="_blank">
                                                <FileText className="h-4 w-4 mr-2" />
                                                Télécharger
                                            </a>
                                        </Button>
                                    </div>
                                )}
                                {reservation.lettre_48n_recto && (
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-2">Lettre 48N (recto)</h4>
                                        <Button variant="outline" size="sm" asChild className="w-full">
                                            <a href={`/files/${reservation.lettre_48n_recto}`} target="_blank">
                                                <FileText className="h-4 w-4 mr-2" />
                                                Télécharger
                                            </a>
                                        </Button>
                                    </div>
                                )}
                                {reservation.lettre_48n_verso && (
                                    <div>
                                        <h4 className="font-medium text-sm text-muted-foreground mb-2">Lettre 48N (verso)</h4>
                                        <Button variant="outline" size="sm" asChild className="w-full">
                                            <a href={`/files/${reservation.lettre_48n_verso}`} target="_blank">
                                                <FileText className="h-4 w-4 mr-2" />
                                                Télécharger
                                            </a>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
