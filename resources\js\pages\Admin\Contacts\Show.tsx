import { Head, router, useForm } from '@inertiajs/react';
import { PageProps, BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowLeft, Mail, Phone, Calendar, User, MessageSquare, CheckCircle, XCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';

interface Contact {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string | null;
  sujet: 'stage' | 'test' | 'pssm' | 'autre';
  message: string;
  lu: boolean;
  reponse: string | null;
  date_reponse: string | null;
  admin_id: number | null;
  admin?: {
    id: number;
    nom: string;
    prenom: string;
  } | null;
  created_at: string;
  updated_at: string;
}

interface ContactShowProps extends PageProps {
  contact: Contact;
}

export default function Show({ contact }: ContactShowProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const { data, setData, post, processing, errors } = useForm({
    reponse: contact.reponse || '',
  });

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    router.delete(route('admin.contacts.destroy', contact.id), {
      onSuccess: () => {
        setIsDeleteDialogOpen(false);
      },
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('admin.contacts.update', contact.id));
  };

  const toggleReadStatus = () => {
    router.patch(route('admin.contacts.toggle-read', contact.id));
  };

  const getSujetLabel = (sujet: string) => {
    switch (sujet) {
      case 'stage':
        return 'Stages de sensibilisation à la sécurité routière';
      case 'test':
        return 'Test psychotechnique';
      case 'pssm':
        return 'Formation PSSM';
      case 'autre':
        return 'Autre demande';
      default:
        return sujet;
    }
  };

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Messages de contact',
      href: route('admin.contacts.index'),
    },
    {
      title: `Message de ${contact.prenom} ${contact.nom}`,
      href: route('admin.contacts.show', contact.id),
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Message de ${contact.prenom} ${contact.nom}`} />

      <div className="container mx-auto py-6">
        <div className="mb-6 flex items-center justify-between">
          <Button variant="outline" onClick={() => router.visit(route('admin.contacts.index'))}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour à la liste
          </Button>

          <div className="flex gap-2">
            <Button
              variant={contact.lu ? 'outline' : 'default'}
              onClick={toggleReadStatus}
            >
              {contact.lu ? (
                <>
                  <XCircle className="mr-2 h-4 w-4" />
                  Marquer comme non lu
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Marquer comme lu
                </>
              )}
            </Button>

            <Button variant="destructive" onClick={handleDelete}>
              Supprimer
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Message</CardTitle>
                  <Badge variant={contact.lu ? 'outline' : 'default'}>
                    {contact.lu ? 'Lu' : 'Non lu'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">{getSujetLabel(contact.sujet)}</h3>
                  <p className="text-sm text-muted-foreground">
                    Reçu le {format(parseISO(contact.created_at), 'dd MMMM yyyy à HH:mm', { locale: fr })}
                  </p>
                </div>

                <Separator />

                <div className="whitespace-pre-wrap rounded-md bg-muted p-4">
                  {contact.message}
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Répondre</CardTitle>
              </CardHeader>
              <form onSubmit={handleSubmit}>
                <CardContent>
                  <Textarea
                    value={data.reponse}
                    onChange={(e) => setData('reponse', e.target.value)}
                    placeholder="Votre réponse..."
                    rows={8}
                    className={errors.reponse ? 'border-red-500' : ''}
                  />
                  {errors.reponse && (
                    <p className="mt-1 text-sm text-red-500">{errors.reponse}</p>
                  )}

                  {contact.reponse && contact.date_reponse && (
                    <p className="mt-2 text-sm text-muted-foreground">
                      Dernière réponse envoyée le {format(parseISO(contact.date_reponse), 'dd MMMM yyyy à HH:mm', { locale: fr })}
                      {contact.admin && ` par ${contact.admin.prenom} ${contact.admin.nom}`}
                    </p>
                  )}
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button type="submit" disabled={processing}>
                    {processing ? 'Envoi en cours...' : (contact.reponse ? 'Mettre à jour la réponse' : 'Envoyer la réponse')}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Informations de contact</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <User className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Nom</p>
                      <p className="text-muted-foreground">{contact.prenom} {contact.nom}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-muted-foreground">{contact.email}</p>
                    </div>
                  </div>

                  {contact.telephone && (
                    <div className="flex items-start gap-3">
                      <Phone className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="font-medium">Téléphone</p>
                        <p className="text-muted-foreground">{contact.telephone}</p>
                      </div>
                    </div>
                  )}

                  <div className="flex items-start gap-3">
                    <MessageSquare className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Sujet</p>
                      <p className="text-muted-foreground">{getSujetLabel(contact.sujet)}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Calendar className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Date de réception</p>
                      <p className="text-muted-foreground">
                        {format(parseISO(contact.created_at), 'dd MMMM yyyy', { locale: fr })}
                      </p>
                      <p className="text-muted-foreground">
                        {format(parseISO(contact.created_at), 'HH:mm', { locale: fr })}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Dialog de confirmation de suppression */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
          </DialogHeader>
          <p>
            Êtes-vous sûr de vouloir supprimer ce message de contact ? Cette action est irréversible.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Supprimer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
