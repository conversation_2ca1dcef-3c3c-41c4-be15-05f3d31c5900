import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';

/**
 * Safely formats a date string using date-fns
 * @param dateString - The date string to format (can be null/undefined)
 * @param formatString - The format pattern (default: 'dd/MM/yyyy')
 * @param fallback - The fallback text when date is invalid (default: 'Date non disponible')
 * @returns Formatted date string or fallback text
 */
export const formatDate = (
    dateString: string | null | undefined, 
    formatString: string = 'dd/MM/yyyy',
    fallback: string = 'Date non disponible'
): string => {
    if (!dateString) return fallback;
    
    try {
        return format(parseISO(dateString), formatString, { locale: fr });
    } catch (error) {
        console.warn('Invalid date string:', dateString, error);
        return fallback;
    }
};

/**
 * Safely formats a date string with time
 * @param dateString - The date string to format (can be null/undefined)
 * @param fallback - The fallback text when date is invalid (default: 'Date non disponible')
 * @returns Formatted date string with time or fallback text
 */
export const formatDateTime = (
    dateString: string | null | undefined,
    fallback: string = 'Date non disponible'
): string => {
    return formatDate(dateString, 'dd/MM/yyyy à HH:mm', fallback);
};

/**
 * Safely formats a date string for display in French
 * @param dateString - The date string to format (can be null/undefined)
 * @param fallback - The fallback text when date is invalid (default: 'Date non disponible')
 * @returns Formatted date string in French or fallback text
 */
export const formatDateFrench = (
    dateString: string | null | undefined,
    fallback: string = 'Date non disponible'
): string => {
    return formatDate(dateString, 'dd MMMM yyyy', fallback);
};
