# Email Notification Setup for Stage Permis

This document explains how to configure and use the email notification system for stage and test reservations.

## Overview

The email notification system automatically sends emails to `<EMAIL>` whenever a reservation is confirmed for either:
- **Stage reservations** (Stages de sensibilisation à la sécurité routière)
- **Test reservations** (tests psychotechniques)

## Features

- ✅ Automatic email notifications when reservations are confirmed
- ✅ Support for both stage and test reservations
- ✅ Queue-based email sending for better performance
- ✅ Comprehensive error handling and logging
- ✅ Configurable email templates
- ✅ Test command for debugging email functionality

## Configuration

### 1. Environment Variables

Update your `.env` file with the appropriate mail configuration:

#### For Development (using Mailpit)
```env
MAIL_MAILER=mailpit
MAIL_HOST=127.0.0.1
MAIL_PORT=1025
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Stage Permis"
ADMIN_EMAIL="<EMAIL>"
```

#### For Production (using SMTP)
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Stage Permis"
ADMIN_EMAIL="<EMAIL>"
```

### 2. Queue Configuration

For better performance, emails are sent using Laravel's queue system. Make sure your queue is configured:

```env
QUEUE_CONNECTION=database
```

Run the queue worker:
```bash
php artisan queue:work
```

## Email Triggers

Emails are automatically sent when:

1. **PayPal payment is completed** (both stage and test reservations)
2. **SumUp payment is completed** (both stage and test reservations)
3. **Payment on-site is selected** (immediately confirmed)
4. **PayPal webhook confirms payment** (backup confirmation)

## Email Content

### Stage Reservation Email
- Reservation details (ID, type, date, status)
- Client information (name, email, phone)
- Stage details (date, time, price, location)
- Payment information (method, transaction ID)

### Test Reservation Email
- Reservation details (ID, type, date, status)
- Client information (name, email, phone)
- Test details (date, time, price, location)
- Payment information (method, transaction ID)

## Testing

### Test Email Functionality

Use the built-in test command to verify email functionality:

```bash
# Test stage reservation notification
php artisan email:test-notification stage

# Test test reservation notification
php artisan email:test-notification test

# Test with specific reservation ID
php artisan email:test-notification stage --id=123

# Test with custom email address
php artisan email:test-notification stage --email=<EMAIL>
```

### Check Email Logs

Monitor email sending in the Laravel logs:
```bash
tail -f storage/logs/laravel.log | grep -i email
```

## Error Handling

The system includes comprehensive error handling:

- **Email failures don't block the payment process**
- **All email attempts are logged**
- **Failed emails are retried automatically via queue**
- **Detailed error messages in logs**

## File Structure

```
app/
├── Console/Commands/
│   └── TestEmailNotification.php      # Test command
├── Http/Controllers/
│   └── PaiementController.php         # Updated with email triggers
├── Notifications/
│   ├── ReservationConfirmedNotification.php      # Stage notification
│   └── TestReservationConfirmedNotification.php  # Test notification
└── Services/
    └── EmailNotificationService.php   # Email service

config/
└── mail.php                          # Updated mail config

.env                                   # Environment configuration
```

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check queue is running: `php artisan queue:work`
   - Verify SMTP credentials in `.env`
   - Check Laravel logs for errors

2. **Emails going to spam**
   - Configure SPF/DKIM records for your domain
   - Use a reputable SMTP service (SendGrid, Mailgun, etc.)

3. **Missing email content**
   - Verify reservation relationships are loaded
   - Check notification class for missing data

### Debug Commands

```bash
# Check mail configuration
php artisan config:show mail

# Test basic email sending
php artisan tinker
>>> Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });

# Check queue status
php artisan queue:work --verbose

# Clear config cache
php artisan config:clear
```

## Production Deployment

1. **Configure production SMTP settings**
2. **Set up queue worker as a service**
3. **Configure email monitoring/alerts**
4. **Test email functionality after deployment**

## Security Considerations

- Store SMTP credentials securely
- Use environment variables for sensitive data
- Monitor email sending for abuse
- Implement rate limiting if needed

## Support

For issues or questions about the email system:
1. Check the Laravel logs first
2. Use the test command to isolate issues
3. Verify configuration settings
4. Check queue worker status
