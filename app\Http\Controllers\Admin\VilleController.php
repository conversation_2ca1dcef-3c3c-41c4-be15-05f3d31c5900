<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Departement;
use App\Models\Ville;
use Illuminate\Http\Request;
use Inertia\Inertia;

class VilleController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/Villes/Index', [
            'villes' => Ville::with('departement', 'lieus')->paginate(10),
            'departements' => Departement::all()
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'departement_id' => 'required|exists:departements,id',
            'code' => 'nullable|string|max:10',
            'reg_code' => 'nullable|string|max:10',
            'dep_code' => 'nullable|string|max:10',
            'code_postal' => 'nullable|string|max:10'
        ]);

        Ville::create($validated);

        return redirect()->back()->with('success', '<PERSON> créée avec succès.');
    }

    public function update(Request $request, $id)
    {
        $ville = Ville::findOrFail($id);

        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'departement_id' => 'required|exists:departements,id',
            'code' => 'nullable|string|max:10',
            'reg_code' => 'nullable|string|max:10',
            'dep_code' => 'nullable|string|max:10',
            'code_postal' => 'nullable|string|max:10'
        ]);

        $ville->update($validated);

        return redirect()->back()->with('success', 'Ville mise à jour avec succès.');
    }

    public function destroy($id)
    {
        $ville = Ville::findOrFail($id);
        $ville->delete();
        return redirect()->back()->with('success', 'Ville supprimée avec succès.');
    }
}


