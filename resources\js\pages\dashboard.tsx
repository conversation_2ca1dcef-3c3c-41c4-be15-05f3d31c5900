import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { CalendarDays, Package, User, Clock, CheckCircle, XCircle, AlertCircle, Users, MapPin, Calendar, BarChart3 } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { Link } from '@inertiajs/react';
import { StageStatusChart } from '@/components/StageStatusChart';
import { TestStatusChart } from '@/components/TestStatusChart';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
];

interface DashboardStats {
    totalStages: number;
    stagesAVenir: number;
    totalReservations: number;
    reservationsConfirmees: number;
    totalTests: number;
    testsAVenir: number;
    totalLieux: number;
    totalClients: number;
    totalDepartements: number;
}

interface RecentActivity {
    id: number;
    type: 'reservation' | 'test';
    date: string;
    statut: string;
    client: {
        id: number;
        nom: string;
        prenom: string;
    };
    details: {
        id: number;
        reference?: string;
        date_debut?: string;
        date?: string;
        lieu: {
            id: number;
            nom: string;
            ville: {
                nom: string;
            };
        };
    };
}

interface DashboardProps {
    dashboardData: {
        stats: DashboardStats;
        recentActivities: RecentActivity[];
        chartData: {
            month: string;
            stages: number;
            tests: number;
        }[];
        stageTrend: {
            percentage: number;
            isUp: boolean;
        };
        testTrend: {
            percentage: number;
            isUp: boolean;
        };
        globalTrend: {
            percentage: number;
            isUp: boolean;
        };
        stageChartData: {
            date: string;
            confirmée: number;
            "en attente": number;
            annulée: number;
        }[];
        testChartData: {
            date: string;
            confirmée: number;
            "en attente": number;
            annulée: number;
        }[];
    };
}

export default function Dashboard({ dashboardData }: DashboardProps) {
    const {
        stats,
        recentActivities,
        stageTrend,
        testTrend,
        stageChartData,
        testChartData
    } = dashboardData;

    // Fonction pour obtenir l'icône et la couleur en fonction du statut
    const getStatusInfo = (statut: string) => {
        switch (statut) {
            case 'confirmée':
                return { icon: <CheckCircle className="h-4 w-4" />, color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' };
            case 'en attente':
                return { icon: <Clock className="h-4 w-4" />, color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' };
            case 'annulée':
                return { icon: <XCircle className="h-4 w-4" />, color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300' };
            default:
                return { icon: <AlertCircle className="h-4 w-4" />, color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300' };
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-4">
                <div className="flex flex-col">
                    <h1 className="text-2xl font-bold tracking-tight">Tableau de bord administrateur</h1>
                    <p className="text-muted-foreground">
                        Vue d'ensemble des activités et statistiques de la plateforme
                    </p>
                </div>

                {/* Statistiques principales */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Stages</CardTitle>
                            <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.totalStages}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.stagesAVenir} stages à venir
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Réservations Stage</CardTitle>
                            <Package className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.totalReservations}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.reservationsConfirmees} confirmées
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Tests Psycho</CardTitle>
                            <User className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.totalTests}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.testsAVenir} tests à venir
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Clients</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.totalClients}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.totalLieux} lieux disponibles
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Graphiques des réservations par statut */}
                <div className="grid gap-6 md:grid-cols-2">
                    <StageStatusChart data={stageChartData} trend={stageTrend}/>
                    <TestStatusChart data={testChartData} trend={testTrend} />
                </div>

                {/* Contenu principal */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {/* Activités récentes */}
                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle>Activité récente</CardTitle>
                            <CardDescription>
                                Les 5 dernières activités sur la plateforme
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {recentActivities && recentActivities.length > 0 ? (
                                <div className="space-y-4">
                                    {recentActivities.map((activity) => {
                                        const statusInfo = getStatusInfo(activity.statut);
                                        const activityIcon = activity.type === 'reservation'
                                            ? <Calendar className="h-10 w-10 rounded-full bg-primary/10 p-2 text-primary" />
                                            : <User className="h-10 w-10 rounded-full bg-primary/10 p-2 text-primary" />;

                                        return (
                                            <div key={`${activity.type}-${activity.id}`} className="flex items-start space-x-4 border-b pb-4 last:border-0 last:pb-0">
                                                <div className="flex-shrink-0">
                                                    {activityIcon}
                                                </div>
                                                <div className="flex-1 space-y-1">
                                                    <div className="flex items-center justify-between">
                                                        <p className="text-sm font-medium">
                                                            {activity.client.prenom} {activity.client.nom}
                                                        </p>
                                                        <Badge className={`${statusInfo.color} flex items-center gap-1`}>
                                                            {statusInfo.icon}
                                                            {activity.statut}
                                                        </Badge>
                                                    </div>
                                                    <p className="text-sm text-muted-foreground">
                                                        {activity.type === 'reservation'
                                                            ? `Réservation pour le stage ${activity.details.reference}`
                                                            : 'Réservation de test psychotechnique'}
                                                    </p>
                                                    <div className="flex items-center text-xs text-muted-foreground">
                                                        <MapPin className="mr-1 h-3 w-3" />
                                                        {activity.details.lieu.nom}, {activity.details.lieu.ville.nom}
                                                    </div>
                                                    <div className="flex items-center text-xs text-muted-foreground">
                                                        <Clock className="mr-1 h-3 w-3" />
                                                        {(() => {
                                                            const dateStr = activity.type === 'reservation'
                                                                ? activity.details.date_debut
                                                                : activity.details.date;
                                                            return dateStr ? format(parseISO(dateStr), 'dd MMMM yyyy', { locale: fr }) : 'Date non disponible';
                                                        })()}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <p className="text-center text-muted-foreground">Aucune activité récente</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Liens rapides */}
                    <Card className="h-fit">
                        <CardHeader>
                            <CardTitle>Accès rapide</CardTitle>
                            <CardDescription>
                                Liens vers les sections principales
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="grid gap-2">
                            <Link
                                href="/admin/stages"
                                className="flex items-center justify-between rounded-lg border p-3 hover:bg-muted transition-colors"
                            >
                                <div className="flex items-center gap-2">
                                    <CalendarDays className="h-5 w-5 text-primary" />
                                    <span>Gestion des stages</span>
                                </div>
                                <Badge variant="outline">{stats.totalStages}</Badge>
                            </Link>
                            <Link
                                href="/admin/reservations"
                                className="flex items-center justify-between rounded-lg border p-3 hover:bg-muted transition-colors"
                            >
                                <div className="flex items-center gap-2">
                                    <Package className="h-5 w-5 text-primary" />
                                    <span>Réservations</span>
                                </div>
                                <Badge variant="outline">{stats.totalReservations}</Badge>
                            </Link>
                            <Link
                                href="/admin/tests-psychos"
                                className="flex items-center justify-between rounded-lg border p-3 hover:bg-muted transition-colors"
                            >
                                <div className="flex items-center gap-2">
                                    <User className="h-5 w-5 text-primary" />
                                    <span>Tests psychotechniques</span>
                                </div>
                                <Badge variant="outline">{stats.totalTests}</Badge>
                            </Link>
                            <Link
                                href="/admin/lieux"
                                className="flex items-center justify-between rounded-lg border p-3 hover:bg-muted transition-colors"
                            >
                                <div className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5 text-primary" />
                                    <span>Lieux</span>
                                </div>
                                <Badge variant="outline">{stats.totalLieux}</Badge>
                            </Link>
                            <Link
                                href="/admin/departements"
                                className="flex items-center justify-between rounded-lg border p-3 hover:bg-muted transition-colors"
                            >
                                <div className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5 text-primary" />
                                    <span>Départements</span>
                                </div>
                                <Badge variant="outline">{stats.totalDepartements}</Badge>
                            </Link>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
