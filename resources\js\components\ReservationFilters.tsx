import React, { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Filter, X, User, CreditCard } from 'lucide-react';
import { Departement, Ville, TypeStage } from '@/types';

interface ReservationFiltersProps {
  filters: {
    search?: string;
    statut?: string;
    type_stage?: string;
    date_debut?: string;
    date_fin?: string;
    customer_search?: string;
    methode_paiement?: string;
    departement?: string;
    ville?: string;
  };
  typeStages: TypeStage[];
  departements: Departement[];
  villes: Ville[];
  routeName: string;
  isArchive?: boolean;
}

export default function ReservationFilters({
  filters,
  typeStages,
  departements,
  villes,
  routeName,
  isArchive = false
}: ReservationFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);
  const [filteredVilles, setFilteredVilles] = useState<Ville[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Filter cities based on selected department
  useEffect(() => {
    if (localFilters.departement && localFilters.departement !== 'all') {
      const filtered = villes.filter(ville =>
        ville.departement_id?.toString() === localFilters.departement
      );
      setFilteredVilles(filtered);
    } else {
      setFilteredVilles(villes);
    }
  }, [localFilters.departement, villes]);

  // Check if any filters are active
  const hasActiveFilters = Object.values(filters).some(value => value && value !== '');

  // Show filters by default if any are active
  useEffect(() => {
    if (hasActiveFilters) {
      setShowFilters(true);
    }
  }, [hasActiveFilters]);

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...localFilters, [key]: value };

    // Reset city when department changes
    if (key === 'departement') {
      newFilters.ville = '';
    }

    setLocalFilters(newFilters);
  };

  const applyFilters = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(localFilters).filter(([_, value]) => value && value !== '')
    );

    console.log('Applying reservation filters:', cleanFilters); // Debug log

    router.get(route(routeName), cleanFilters, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const clearFilters = () => {
    setLocalFilters({});
    router.get(route(routeName), {}, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const clearIndividualFilter = (filterKey: string) => {
    const newFilters = { ...localFilters };
    delete newFilters[filterKey];
    setLocalFilters(newFilters);
    
    const cleanFilters = Object.fromEntries(
      Object.entries(newFilters).filter(([_, value]) => value && value !== '')
    );

    router.get(route(routeName), cleanFilters, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applyFilters();
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres des réservations
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="text-sm"
              >
                <X className="h-4 w-4 mr-1" />
                Effacer tout
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              {showFilters ? 'Masquer' : 'Afficher'} les filtres
            </Button>
          </div>
        </div>
        
        {/* Active filters indicators */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 mt-2">
            {Object.entries(filters).map(([key, value]) => {
              if (!value) return null;
              
              let displayValue = value;
              let displayLabel = key;
              
              // Customize display labels and values
              switch (key) {
                case 'statut':
                  displayLabel = 'Statut';
                  break;
                case 'type_stage':
                  displayLabel = 'Type';
                  const typeStage = typeStages.find(t => t.id.toString() === value);
                  displayValue = typeStage?.nom || value;
                  break;
                case 'customer_search':
                  displayLabel = 'Client';
                  break;
                case 'methode_paiement':
                  displayLabel = 'Paiement';
                  break;
                case 'date_debut':
                  displayLabel = 'Date début';
                  break;
                case 'date_fin':
                  displayLabel = 'Date fin';
                  break;
                case 'departement':
                  displayLabel = 'Département';
                  const dept = departements.find(d => d.id.toString() === value);
                  displayValue = dept ? `${dept.code} - ${dept.nom}` : value;
                  break;
                case 'ville':
                  displayLabel = 'Ville';
                  const ville = villes.find(v => v.id.toString() === value);
                  displayValue = ville?.nom || value;
                  break;
                case 'search':
                  displayLabel = 'Recherche';
                  break;
              }
              
              return (
                <div
                  key={key}
                  className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm"
                >
                  <span className="font-medium">{displayLabel}:</span>
                  <span>{displayValue}</span>
                  <button
                    onClick={() => clearIndividualFilter(key)}
                    className="ml-1 hover:bg-blue-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </CardHeader>

      {showFilters && (
        <CardContent className="space-y-4">
          {/* First row: Search and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Rechercher par référence</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Référence stage..."
                  value={localFilters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="statut">Statut</Label>
              <Select
                value={localFilters.statut || 'all'}
                onValueChange={(value) => handleFilterChange('statut', value === 'all' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="confirmée">Confirmée</SelectItem>
                  <SelectItem value="en attente">En attente</SelectItem>
                  <SelectItem value="annulée">Annulée</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type_stage">Type de stage</Label>
              <Select
                value={localFilters.type_stage || 'all'}
                onValueChange={(value) => handleFilterChange('type_stage', value === 'all' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les types</SelectItem>
                  {typeStages.map((type) => (
                    <SelectItem key={type.id} value={type.id.toString()}>
                      {type.nom}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer_search">Rechercher client</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="customer_search"
                  placeholder="Nom, prénom ou email..."
                  value={localFilters.customer_search || ''}
                  onChange={(e) => handleFilterChange('customer_search', e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Second row: Location and Payment */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="departement">Département</Label>
              <Select
                value={localFilters.departement || 'all'}
                onValueChange={(value) => handleFilterChange('departement', value === 'all' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les départements" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les départements</SelectItem>
                  {departements.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id.toString()}>
                      {dept.code} - {dept.nom}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ville">Ville</Label>
              <Select
                value={localFilters.ville || 'all'}
                onValueChange={(value) => handleFilterChange('ville', value === 'all' ? '' : value)}
                disabled={!localFilters.departement || localFilters.departement === 'all'}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les villes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les villes</SelectItem>
                  {filteredVilles.map((ville) => (
                    <SelectItem key={ville.id} value={ville.id.toString()}>
                      {ville.nom}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="methode_paiement">Méthode de paiement</Label>
              <div className="relative">
                <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="methode_paiement"
                  placeholder="Carte, espèces, virement..."
                  value={localFilters.methode_paiement || ''}
                  onChange={(e) => handleFilterChange('methode_paiement', e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Third row: Date range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date_debut">Date de réservation (début)</Label>
              <Input
                id="date_debut"
                type="date"
                value={localFilters.date_debut || ''}
                onChange={(e) => handleFilterChange('date_debut', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="date_fin">Date de réservation (fin)</Label>
              <Input
                id="date_fin"
                type="date"
                value={localFilters.date_fin || ''}
                onChange={(e) => handleFilterChange('date_fin', e.target.value)}
              />
            </div>
          </div>

          {/* Apply filters button */}
          <div className="flex justify-end pt-4">
            <Button onClick={applyFilters} className="min-w-[120px]">
              <Search className="h-4 w-4 mr-2" />
              Appliquer les filtres
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
