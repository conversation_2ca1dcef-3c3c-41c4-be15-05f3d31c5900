<?php

namespace App\Notifications;

use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomerReservationConfirmedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The reservation instance.
     *
     * @var \App\Models\Reservation
     */
    protected $reservation;

    /**
     * Create a new notification instance.
     */
    public function __construct(Reservation $reservation)
    {
        $this->reservation = $reservation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $reservation = $this->reservation;
        $user = $reservation->user;
        $stage = $reservation->stage;
        $typeStage = $reservation->typeStage;

        return (new MailMessage)
            ->subject('Confirmation de votre réservation - Stages de sensibilisation à la sécurité routière #' . $reservation->id)
            ->view('emails.customer-reservation-confirmed', [
                'reservation' => $reservation,
                'user' => $user,
                'stage' => $stage,
                'typeStage' => $typeStage,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'reservation_id' => $this->reservation->id,
            'user_name' => $this->reservation->user->nom . ' ' . $this->reservation->user->prenom,
            'user_email' => $this->reservation->user->email,
            'stage_date' => $this->reservation->stage->date_debut->format('Y-m-d'),
            'type_stage' => $this->reservation->typeStage->nom ?? null,
            'statut' => $this->reservation->statut,
            'methode_paiement' => $this->reservation->methode_paiement,
        ];
    }
}
