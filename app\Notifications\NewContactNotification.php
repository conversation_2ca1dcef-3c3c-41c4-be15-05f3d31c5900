<?php

namespace App\Notifications;

use App\Models\Contact;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewContactNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The contact instance.
     *
     * @var \App\Models\Contact
     */
    protected $contact;

    /**
     * Create a new notification instance.
     */
    public function __construct(Contact $contact)
    {
        $this->contact = $contact;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $sujets = [
            'stage' => 'Stages de sensibilisation à la sécurité routière',
            'test' => 'Test psychotechnique',
            'pssm' => 'Formation PSSM',
            'autre' => 'Autre demande'
        ];

        $sujetTexte = $sujets[$this->contact->sujet] ?? 'Demande de contact';

        return (new MailMessage)
            ->subject('Nouveau message de contact - ' . $sujetTexte)
            ->view('emails.new-contact', [
                'contact' => $this->contact,
                'sujetTexte' => $sujetTexte,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'contact_id' => $this->contact->id,
            'nom' => $this->contact->nom,
            'prenom' => $this->contact->prenom,
            'email' => $this->contact->email,
            'sujet' => $this->contact->sujet,
        ];
    }
}
