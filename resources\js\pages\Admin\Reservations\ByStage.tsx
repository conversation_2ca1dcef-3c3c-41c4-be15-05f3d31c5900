import { Head, router } from '@inertiajs/react';
import { Stage, Reservation, TypeStage, User, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import TabNavigation from '@/components/TabNavigation';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowLeft, Eye } from 'lucide-react';

interface ByStageProps {
    reservations: PaginatedData<Reservation>;
    stage: Stage;
    stages: Stage[];
    typeStages: TypeStage[];
    users: User[];
    isArchive?: boolean;
}

export default function ByStage({ reservations, stage, stages, typeStages, users, isArchive = false }: ByStageProps) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'confirmée':
                return <Badge className="bg-green-500">Confirmée</Badge>;
            case 'en attente':
                return <Badge className="bg-yellow-500">En attente</Badge>;
            case 'annulée':
                return <Badge className="bg-red-500">Annulée</Badge>;
            default:
                return <Badge>{status}</Badge>;
        }
    };

    const columns = [
        { key: 'id', label: 'ID' },
        {
            key: 'user',
            label: 'Client',
            render: (_value: unknown, row: Record<string, unknown>) => {
                const reservation = row as unknown as Reservation;
                return reservation.user ? (
                    <div className="flex flex-col">
                        <span className="font-medium">{reservation.user.prenom} {reservation.user.nom}</span>
                        <span className="text-xs text-gray-500">{reservation.user.email}</span>
                    </div>
                ) : (
                    ''
                );
            },
        },
        {
            key: 'type_stage',
            index: 'type_stage_id',
            label: 'Type de stage',
            render: (_value: unknown, row: Record<string, unknown>) => {
                const reservation = row as unknown as Reservation;
                return reservation.type_stage?.nom || '';
            },
        },
        {
            key: 'date_reservation',
            label: 'Date de réservation',
            render: (value: unknown) => {
                const date = value as string;
                return format(parseISO(date), 'dd/MM/yyyy HH:mm', { locale: fr });
            },
        },
        {
            key: 'statut',
            label: 'Statut',
            render: (value: unknown) => {
                return getStatusBadge(value as string);
            },
        },
    ];

    const handleEdit = (row: Record<string, unknown>) => {
        const reservation = row as unknown as Reservation;
        // Navigate to the main reservations page with edit functionality
        router.visit(route('admin.reservations.index'), {
            data: { edit: reservation.id },
            preserveState: true,
        });
    };

    const handleDelete = (row: Record<string, unknown>) => {
        const reservation = row as unknown as Reservation;
        if (confirm('Êtes-vous sûr de vouloir supprimer cette réservation ?')) {
            router.delete(route('admin.reservations.destroy', reservation.id));
        }
    };

    const handleShow = (row: Record<string, unknown>) => {
        const reservation = row as unknown as Reservation;
        router.visit(route('admin.reservations.show', reservation.id));
    };

    const tabs = [
        {
            name: 'Réservations actuelles',
            href: route('admin.reservations.by-stage', stage.id),
            current: !isArchive,
        },
        {
            name: 'Archives',
            href: route('admin.reservations.by-stage', { stage: stage.id, archive: true }),
            current: isArchive,
        },
    ];

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Stages',
            href: '/admin/stages',
        },
        {
            title: `Réservations - ${stage.reference}`,
            href: `/admin/stages/${stage.id}/reservations`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Réservations - ${stage.reference}`} />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="flex items-center gap-4 mb-6">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.visit(route('admin.stages.index'))}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Retour aux stages
                    </Button>
                    <div className="flex-1">
                        <h1 className="text-2xl font-bold">Réservations pour le stage {stage.reference}</h1>
                        <p className="text-muted-foreground">
                            {stage.lieu?.nom} - {stage.lieu?.ville?.nom} ({stage.lieu?.ville?.departement?.code})
                            <br />
                            {format(parseISO(stage.date_debut), 'dd/MM/yyyy', { locale: fr })} - {stage.prix}€
                        </p>
                    </div>
                </div>

                <TabNavigation tabs={tabs} />

                <DataTable
                    title={`${isArchive ? 'Archives' : 'Réservations actuelles'} (${reservations.total})`}
                    columns={columns}
                    data={reservations.data.map(reservation => ({
                        id: reservation.id,
                        user: reservation.user,
                        type_stage: reservation.type_stage,
                        type_stage_id: reservation.type_stage_id,
                        date_reservation: reservation.date_reservation,
                        statut: reservation.statut,
                    }))}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onShow={handleShow}
                    pagination={{
                        links: reservations.links,
                        from: reservations.from,
                        to: reservations.to,
                        total: reservations.total
                    }}
                />
            </div>
        </AppLayout>
    );
}
