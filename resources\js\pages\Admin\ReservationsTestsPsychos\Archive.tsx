import Index from './Index';
import { PageProps, PaginatedData, ReservationTestPsycho, TestPsycho, TypeTestPsycho, User } from '@/types';

interface ReservationsTestsPsychosPageProps extends PageProps {
  reservations: PaginatedData<ReservationTestPsycho>;
  tests: TestPsycho[];
  typeTests: TypeTestPsycho[];
  users: User[];
  isArchive?: boolean;
}

export default function Archive(props: ReservationsTestsPsychosPageProps) {
    return <Index {...props} />;
}
