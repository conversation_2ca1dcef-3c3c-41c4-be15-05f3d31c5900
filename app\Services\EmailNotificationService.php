<?php

namespace App\Services;

use App\Models\Reservation;
use App\Models\ReservationTestPsycho;
use App\Models\User;
use App\Notifications\ReservationConfirmedNotification;
use App\Notifications\TestReservationConfirmedNotification;
use App\Notifications\CustomerReservationConfirmedNotification;
use App\Notifications\CustomerTestReservationConfirmedNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class EmailNotificationService
{
    /**
     * Send notification for confirmed stage reservation
     *
     * @param Reservation $reservation
     * @param bool $sendToCustomer Whether to send notification to customer (default: true)
     * @param bool $sendToAdmin Whether to send notification to admin (default: true)
     * @return bool
     */
    public function sendStageReservationNotification(Reservation $reservation, bool $sendToCustomer = true, bool $sendToAdmin = true): bool
    {
        $success = true;

        try {
            // Send notification to admins
            if ($sendToAdmin) {
                $admins = User::where('role', 'admin')->get();
                if ($admins->isNotEmpty()) {
                    Notification::send($admins, new ReservationConfirmedNotification($reservation));

                    Log::info('Stage reservation admin notification sent successfully', [
                        'reservation_id' => $reservation->id,
                        'admin_count' => $admins->count(),
                        'user_email' => $reservation->user->email,
                        'stage_date' => $reservation->stage->date_debut->format('Y-m-d'),
                    ]);
                }
            }

            // Send notification to customer
            if ($sendToCustomer && $reservation->user && $reservation->user->email) {
                $this->sendCustomerStageReservationNotification($reservation);
            }

            return $success;
        } catch (\Exception $e) {
            Log::error('Failed to send stage reservation notification', [
                'reservation_id' => $reservation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send notification for confirmed test reservation
     *
     * @param ReservationTestPsycho $reservation
     * @param bool $sendToCustomer Whether to send notification to customer (default: true)
     * @param bool $sendToAdmin Whether to send notification to admin (default: true)
     * @return bool
     */
    public function sendTestReservationNotification(ReservationTestPsycho $reservation, bool $sendToCustomer = true, bool $sendToAdmin = true): bool
    {
        $success = true;

        try {
            // Send notification to admins
            if ($sendToAdmin) {
                $admins = User::where('role', 'admin')->get();
                if ($admins->isNotEmpty()) {
                    Notification::send($admins, new TestReservationConfirmedNotification($reservation));

                    Log::info('Test reservation admin notification sent successfully', [
                        'reservation_id' => $reservation->id,
                        'admin_count' => $admins->count(),
                        'user_email' => $reservation->user->email,
                        'test_date' => $reservation->testPsycho->date->format('Y-m-d'),
                    ]);
                }
            }

            // Send notification to customer
            if ($sendToCustomer && $reservation->user && $reservation->user->email) {
                $this->sendCustomerTestReservationNotification($reservation);
            }

            return $success;
        } catch (\Exception $e) {
            Log::error('Failed to send test reservation notification', [
                'reservation_id' => $reservation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }


    /**
     * Send customer notification for confirmed stage reservation
     *
     * @param Reservation $reservation
     * @return bool
     */
    public function sendCustomerStageReservationNotification(Reservation $reservation): bool
    {
        try {
            if (!$reservation->user || !$reservation->user->email) {
                Log::warning('Cannot send customer stage notification: no user or email', [
                    'reservation_id' => $reservation->id,
                ]);
                return false;
            }

            // Send notification to customer
            Notification::send([$reservation->user], new CustomerReservationConfirmedNotification($reservation));

            Log::info('Stage reservation customer notification sent successfully', [
                'reservation_id' => $reservation->id,
                'customer_email' => $reservation->user->email,
                'stage_date' => $reservation->stage->date_debut->format('Y-m-d'),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send customer stage reservation notification', [
                'reservation_id' => $reservation->id,
                'customer_email' => $reservation->user->email ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send customer notification for confirmed test reservation
     *
     * @param ReservationTestPsycho $reservation
     * @return bool
     */
    public function sendCustomerTestReservationNotification(ReservationTestPsycho $reservation): bool
    {
        try {
            if (!$reservation->user || !$reservation->user->email) {
                Log::warning('Cannot send customer test notification: no user or email', [
                    'reservation_id' => $reservation->id,
                ]);
                return false;
            }

            // Send notification to customer
            Notification::send([$reservation->user], new CustomerTestReservationConfirmedNotification($reservation));

            Log::info('Test reservation customer notification sent successfully', [
                'reservation_id' => $reservation->id,
                'customer_email' => $reservation->user->email,
                'test_date' => $reservation->testPsycho->date->format('Y-m-d'),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send customer test reservation notification', [
                'reservation_id' => $reservation->id,
                'customer_email' => $reservation->user->email ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Check if reservation should trigger email notification
     *
     * @param Reservation|ReservationTestPsycho $reservation
     * @return bool
     */
    public function shouldSendNotification($reservation): bool
    {
        // Send notification only when status is 'confirmée'
        return $reservation->statut === 'confirmée';
    }
}
