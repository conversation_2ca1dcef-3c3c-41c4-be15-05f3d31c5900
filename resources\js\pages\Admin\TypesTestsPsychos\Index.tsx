import { Head, router } from '@inertiajs/react';
import { PageProps, TypeTestPsycho, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';

interface TypesTestsPsychosPageProps extends PageProps {
    types: PaginatedData<TypeTestPsycho>;
}

export default function Index({ types }: TypesTestsPsychosPageProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [editingType, setEditingType] = useState<TypeTestPsycho | null>(null);

    const form = useForm({
        nom: '',
        description: '',
    });

    const columns = [
        {
            key: 'nom',
            label: 'Nom',
        },
        {
            key: 'description',
            label: 'Description',
        },
        {
            key: 'reservations_count',
            label: 'Nombre de réservations',
            render: (value: unknown, row: Record<string, unknown>) => {
                const count = (value || 0) as number;
                const typeTest = row as unknown as TypeTestPsycho;

                if (count === 0) {
                    return <span className="text-muted-foreground">0</span>;
                }

                return (
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 hover:text-blue-800 font-medium"
                        onClick={() => router.visit(route('admin.reservations-tests-psychos.by-type-test', typeTest.id))}
                    >
                        {count}
                    </Button>
                );
            },
        },
    ];

    const handleAdd = () => {
        setEditingType(null);
        form.reset();
        setIsOpen(true);
    };

    const handleEdit = (type: TypeTestPsycho) => {
        setEditingType(type);
        form.setData({
            nom: type.nom,
            description: type.description || '',
        });
        setIsOpen(true);
    };

    const handleDelete = (type: TypeTestPsycho) => {
        if (confirm(`Êtes-vous sûr de vouloir supprimer le type de test psychotechnique "${type.nom}" ?`)) {
            router.delete(`/admin/types-tests-psychos/${type.id}`);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingType) {
            form.put(`/admin/types-tests-psychos/${editingType.id}`, {
                onSuccess: () => setIsOpen(false),
            });
        } else {
            form.post('/admin/types-tests-psychos', {
                onSuccess: () => setIsOpen(false),
            });
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Types de tests psychotechniques',
            href: '/admin/types-tests-psychos',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Types de tests psychotechniques" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <DataTable
                    title="Types de tests psychotechniques"
                    columns={columns}
                    data={types.data.map(type => ({
                        id: type.id,
                        nom: type.nom,
                        description: type.description,
                        reservations_count: type.reservations_count,
                    }))}
                    onAdd={handleAdd}
                    onEdit={handleEdit}
                    onDelete={(row) => handleDelete(row as unknown as TypeTestPsycho)}
                    pagination={{
                        links: types.links,
                        from: types.from,
                        to: types.to,
                        total: types.total
                    }}
                />
            </div>

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {editingType ? 'Modifier le type de test psychotechnique' : 'Ajouter un type de test psychotechnique'}
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label htmlFor="nom" className="text-sm font-medium">Nom</label>
                            <Input
                                id="nom"
                                value={form.data.nom}
                                onChange={e => form.setData('nom', e.target.value)}
                                required
                            />
                            {form.errors.nom && <p className="text-sm text-red-500">{form.errors.nom}</p>}
                        </div>
                        <div className="space-y-2">
                            <label htmlFor="description" className="text-sm font-medium">Description</label>
                            <Textarea
                                id="description"
                                value={form.data.description}
                                onChange={e => form.setData('description', e.target.value)}
                                rows={4}
                            />
                            {form.errors.description && <p className="text-sm text-red-500">{form.errors.description}</p>}
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingType ? 'Mettre à jour' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
