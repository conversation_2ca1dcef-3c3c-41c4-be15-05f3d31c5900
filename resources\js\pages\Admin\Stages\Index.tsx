import { Head, router } from '@inertiajs/react';
import { AdminStagesProps, Stage, BreadcrumbItem } from '@/types';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { DataTableColumnHeader } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO, isBefore, addDays } from 'date-fns';
import { fr } from 'date-fns/locale';
import TabNavigation from '@/components/TabNavigation';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import InputError from '@/components/input-error';
import ValidationErrors from '@/components/validation-errors';
import AdminFilters from '@/components/AdminFilters';
import { Pencil, Trash2 } from 'lucide-react';

export default function Index({ stages, lieux, departements, villes, filters, isArchive = false }: AdminStagesProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isImportOpen, setIsImportOpen] = useState(false);
    const [editingStage, setEditingStage] = useState<Stage | null>(null);
    const [frontendErrors, setFrontendErrors] = useState<Record<string, string>>({});

    const form = useForm({
        date_debut: '',
        lieu_id: '',
        places_disponibles: '20',
        prix: '220',
        reference: '',
    });

    const importForm = useForm({
        file: null as File | null,
    });

    // Fonction de validation frontend
    const validateForm = () => {
        const errors: Record<string, string> = {};
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Validation référence
        if (!form.data.reference.trim()) {
            errors.reference = 'La référence est obligatoire.';
        } else if (form.data.reference.length < 3) {
            errors.reference = 'La référence doit contenir au moins 3 caractères.';
        }

        // Validation date de début
        if (!form.data.date_debut) {
            errors.date_debut = 'La date de début est obligatoire.';
        } else {
            const dateDebut = new Date(form.data.date_debut);
            if (!editingStage && isBefore(dateDebut, today)) {
                errors.date_debut = 'La date de début doit être dans le futur.';
            }
        }

        // La date de fin est automatiquement calculée (date_debut + 1 jour)

        // Validation lieu
        if (!form.data.lieu_id) {
            errors.lieu_id = 'Le lieu est obligatoire.';
        }

        // Validation places disponibles
        if (!form.data.places_disponibles) {
            errors.places_disponibles = 'Le nombre de places est obligatoire.';
        } else {
            const places = parseInt(form.data.places_disponibles);
            if (isNaN(places) || places < 1) {
                errors.places_disponibles = 'Le nombre de places doit être au moins 1.';
            } else if (places > 100) {
                errors.places_disponibles = 'Le nombre de places ne peut pas dépasser 100.';
            }
        }

        // Validation prix
        if (!form.data.prix) {
            errors.prix = 'Le prix est obligatoire.';
        } else {
            const prix = parseFloat(form.data.prix);
            if (isNaN(prix) || prix < 0) {
                errors.prix = 'Le prix doit être un nombre positif.';
            } else if (prix > 10000) {
                errors.prix = 'Le prix ne peut pas dépasser 10 000€.';
            }
        }

        setFrontendErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const columns: ColumnDef<Stage>[] = [
        {
            accessorKey: "reference",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Référence" />
            ),
            enableSorting: true,
            enableHiding: false,
        },
        {
            accessorKey: "date_debut",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Dates" />
            ),
            cell: ({ row }) => {
                const date = row.getValue("date_debut") as string;
                return <span>
                    {format(parseISO(date), 'dd/MM/yyyy', { locale: fr })} <br />
                    {format(parseISO(row.original.date_fin), 'dd/MM/yyyy', { locale: fr })}
                </span>;
            },
            enableSorting: true,
        },
        {
            id: "lieu",
            header: "Lieu",
            cell: ({ row }) => {
                const stage = row.original;
                return (
                    <div className="flex flex-col">
                        <span className="font-medium">{stage.lieu?.nom}</span>
                        <span className="text-sm text-muted-foreground">
                            {stage.lieu?.ville?.nom} ({stage.lieu?.ville?.departement?.code})
                        </span>
                    </div>
                );
            },
            enableSorting: false,
        },
        // {
        //     accessorKey: "places_disponibles",
        //     header: ({ column }) => (
        //         <DataTableColumnHeader column={column} title="Places disponibles" />
        //     ),
        //     enableSorting: true,
        // },
        {
            accessorKey: "prix",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Prix" />
            ),
            cell: ({ row }) => {
                const prix = row.getValue("prix") as number;
                return `${prix} €`;
            },
            enableSorting: true,
        },
        {
            accessorKey: "reservations_count",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Réservations" />
            ),
            cell: ({ row }) => {
                const stage = row.original;
                const count = row.getValue("reservations_count") as number;

                if (count === 0) {
                    return <span className="text-muted-foreground">0</span>;
                }

                return (
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 hover:text-blue-800 font-medium"
                        onClick={() => router.visit(route('admin.reservations.by-stage', stage.id))}
                    >
                        {count}
                    </Button>
                );
            },
            enableSorting: true,
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => {
                const stage = row.original;
                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEdit(stage)}
                            className="h-8 w-8"
                        >
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Modifier</span>
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(stage)}
                            className="h-8 w-8 text-destructive hover:text-destructive"
                        >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Supprimer</span>
                        </Button>
                    </div>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
    ];

    const handleAdd = () => {
        setEditingStage(null);
        form.reset();
        // Appliquer les valeurs par défaut après le reset
        form.setData({
            date_debut: '',
            lieu_id: '',
            places_disponibles: '20',
            prix: '220',
            reference: '',
        });
        setFrontendErrors({});
        setIsOpen(true);
    };

    const handleImport = () => {
        importForm.reset();
        setIsImportOpen(true);
    };

    const handleImportSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        importForm.post(route('admin.stages.import'), {
            forceFormData: true,
            onSuccess: () => {
                setIsImportOpen(false);
                toast.success('Stages importés avec succès');
            },
            onError: (errors) => {
                if (errors.file && Array.isArray(errors.file)) {
                    // Afficher chaque erreur de validation dans une notification séparée
                    errors.file.forEach((error) => {
                        toast.error(error);
                    });
                } else if (errors.file) {
                    toast.error(errors.file);
                } else {
                    toast.error('Erreur lors de l\'importation : ' + (errors.message || 'Erreur inconnue'));
                }
            }
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            importForm.setData('file', e.target.files[0]);
        }
    };

    const handleEdit = (stage: Stage) => {
        console.log('Stage data:', stage);
        setEditingStage(stage);
        setFrontendErrors({});

        // Vérifier que les propriétés existent avant d'appeler toString()
        const lieu_id = stage.lieu?.id !== undefined ? stage.lieu?.id.toString() : '';
        const places_disponibles = stage.places_disponibles !== undefined ? stage.places_disponibles.toString() : '';
        const prix = stage.prix !== undefined ? stage.prix.toString() : '';

        const formData = {
            date_debut: stage.date_debut || '',
            lieu_id: lieu_id,
            places_disponibles: places_disponibles,
            prix: prix,
            reference: stage.reference || '',
        };

        console.log('Form data:', formData);
        form.setData(formData);
        setIsOpen(true);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Validation frontend
        if (!validateForm()) {
            toast.error('Veuillez corriger les erreurs dans le formulaire');
            return;
        }

        if (editingStage) {
            form.put(route('admin.stages.update', editingStage.id), {
                onSuccess: () => {
                    setIsOpen(false);
                    setFrontendErrors({});
                    toast.success('Stage modifié avec succès');
                },
                onError: (errors) => {
                    console.log('Erreurs de validation:', errors);
                }
            });
        } else {
            form.post(route('admin.stages.store'), {
                onSuccess: () => {
                    setIsOpen(false);
                    setFrontendErrors({});
                    toast.success('Stage ajouté avec succès');
                },
                onError: (errors) => {
                    console.log('Erreurs de validation:', errors);
                }
            });
        }
    };

    const handleDelete = (stage: Stage) => {
        if (confirm('Êtes-vous sûr de vouloir supprimer ce stage ?')) {
            router.delete(route('admin.stages.destroy', stage.id));
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Stages',
            href: '/admin/stages',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Stages" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <TabNavigation
                    tabs={[
                        {
                            name: 'Stages à venir',
                            href: route('admin.stages.index'),
                            current: !isArchive,
                        },
                        {
                            name: 'Archives',
                            href: route('admin.stages.archive'),
                            current: isArchive,
                        },
                    ]}
                />

                <AdminFilters
                    filters={filters}
                    departements={departements}
                    villes={villes}
                    routeName={isArchive ? 'admin.stages.archive' : 'admin.stages.index'}
                    isArchive={isArchive}
                />

                <DataTable
                    title={isArchive ? "Archives des stages" : "Stages"}
                    columns={columns}
                    data={stages.data}
                    onAdd={!isArchive ? handleAdd : undefined}
                    onImport={!isArchive ? handleImport : undefined}
                    pagination={{
                        links: stages.links,
                        from: stages.from,
                        to: stages.to,
                        total: stages.total
                    }}
                />
            </div>

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[900px]">
                    <DialogHeader>
                        <DialogTitle>
                            {editingStage ? 'Modifier le stage' : 'Ajouter un stage'}
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <Label htmlFor="reference">Référence du stage</Label>
                            <Input
                                id="reference"
                                placeholder="Référence du stage"
                                value={form.data.reference}
                                onChange={e => {
                                    form.setData('reference', e.target.value);
                                    if (frontendErrors.reference) {
                                        setFrontendErrors(prev => ({ ...prev, reference: '' }));
                                    }
                                }}
                            />
                            <InputError message={frontendErrors.reference || form.errors.reference} />
                        </div>
                        <div>
                            <Label htmlFor="date_debut">Date du stage</Label>
                            <Input
                                id="date_debut"
                                type="date"
                                value={form.data.date_debut}
                                onChange={e => {
                                    form.setData('date_debut', e.target.value);
                                    if (frontendErrors.date_debut) {
                                        setFrontendErrors(prev => ({ ...prev, date_debut: '' }));
                                    }
                                }}
                            />
                            <InputError message={frontendErrors.date_debut || form.errors.date_debut} />
                            <p className="text-sm text-gray-500 mt-1">
                                Le stage durera 2 jours (du {form.data.date_debut ? format(new Date(form.data.date_debut), 'dd/MM/yyyy', { locale: fr }) : '...'} au {form.data.date_debut ? format(addDays(new Date(form.data.date_debut), 1), 'dd/MM/yyyy', { locale: fr }) : '...'})
                            </p>
                        </div>
                        <div>
                            <Label htmlFor="lieu_id">Lieu</Label>
                            <Select
                                value={form.data.lieu_id}
                                onValueChange={(value) => {
                                    form.setData('lieu_id', value);
                                    if (frontendErrors.lieu_id) {
                                        setFrontendErrors(prev => ({ ...prev, lieu_id: '' }));
                                    }
                                }}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Sélectionner un lieu" />
                                </SelectTrigger>
                                <SelectContent>
                                    {lieux.map((lieu) => (
                                        <SelectItem
                                            key={lieu.id}
                                            value={lieu.id.toString()}
                                        >
                                            {lieu.nom} - {lieu.ville?.nom}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <InputError message={frontendErrors.lieu_id || form.errors.lieu_id} />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="places_disponibles">Places disponibles</Label>
                                <Input
                                    id="places_disponibles"
                                    type="number"
                                    placeholder="Places disponibles"
                                    value={form.data.places_disponibles}
                                    onChange={e => {
                                        form.setData('places_disponibles', e.target.value);
                                        if (frontendErrors.places_disponibles) {
                                            setFrontendErrors(prev => ({ ...prev, places_disponibles: '' }));
                                        }
                                    }}
                                    min="1"
                                    max="100"
                                />
                                <InputError message={frontendErrors.places_disponibles || form.errors.places_disponibles} />
                            </div>
                            <div>
                                <Label htmlFor="prix">Prix (€)</Label>
                                <Input
                                    id="prix"
                                    type="number"
                                    placeholder="Prix (€)"
                                    value={form.data.prix}
                                    onChange={e => {
                                        form.setData('prix', e.target.value);
                                        if (frontendErrors.prix) {
                                            setFrontendErrors(prev => ({ ...prev, prix: '' }));
                                        }
                                    }}
                                    min="0"
                                    max="10000"
                                    step="0.01"
                                />
                                <InputError message={frontendErrors.prix || form.errors.prix} />
                            </div>
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingStage ? 'Mettre à jour' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            Importer des stages
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleImportSubmit} className="space-y-4">
                        <ValidationErrors errors={importForm.errors} />
                        <div>
                            <Label htmlFor="file">Fichier Excel</Label>
                            <Input
                                id="file"
                                type="file"
                                accept=".xlsx,.xls"
                                onChange={handleFileChange}
                            />
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                            <p>Le fichier doit contenir les colonnes suivantes :</p>
                            <ul className="list-disc pl-5 mt-2">
                                <li>dates (format: DD/MM/YYYY)</li>
                                <li>prix</li>
                                <li>places_disponibles</li>
                                <li>lieu_id</li>
                            </ul>
                            <p className="mt-2">La date de fin sera automatiquement calculée en ajoutant un jour à la date de début.</p>
                            <p className="mt-2">
                                Veuillez utiliser un fichier Excel (.xlsx ou .xls) avec les colonnes indiquées ci-dessus.
                            </p>
                            <p className="mt-2">
                                <strong>Note:</strong> Les dates peuvent être au format texte (JJ/MM/AAAA) ou au format numérique Excel.
                            </p>
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsImportOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={importForm.processing}>
                                Importer
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}



