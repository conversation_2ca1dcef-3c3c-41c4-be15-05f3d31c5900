import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { PlusIcon, Pencil, Trash2, UploadIcon, Eye } from 'lucide-react';
import Pagination from './Pagination';

interface Column {
    key: string;
    label: string;
    render?: (value: unknown, row: Record<string, unknown>) => React.ReactNode;
}

interface DataTableProps {
    title: string;
    columns: Column[];
    data: Record<string, unknown>[];
    onAdd?: () => void;
    onImport?: () => void;
    onEdit?: (row: Record<string, unknown>) => void;
    onDelete?: (row: Record<string, unknown>) => void;
    onShow?: (row: Record<string, unknown>) => void;
    pagination?: {
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
        from: number;
        to: number;
        total: number;
    };
}

export default function DataTable({
    title,
    columns,
    data,
    onAdd,
    onImport,
    onEdit,
    onDelete,
    onShow,
    pagination,
}: DataTableProps) {
    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>{title}</CardTitle>
                <div className="flex space-x-2">
                    {onImport && (
                        <Button onClick={onImport} variant="outline">
                            <UploadIcon className="mr-2 h-4 w-4" />
                            Importer
                        </Button>
                    )}
                    {onAdd && (
                        <Button onClick={onAdd}>
                            <PlusIcon className="mr-2 h-4 w-4" />
                            Ajouter
                        </Button>
                    )}
                </div>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            {columns.map((column) => (
                                <TableHead key={column.key}>{column.label}</TableHead>
                            ))}
                            {(onEdit || onDelete || onShow) && <TableHead className="w-[150px]">Actions</TableHead>}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {data.map((row, i) => (
                            <TableRow key={i}>
                                {columns.map((column) => (
                                    <TableCell key={column.key}>
                                        {column.render
                                            ? column.render(row[column.key], row)
                                            : (row[column.key] as React.ReactNode)}
                                    </TableCell>
                                ))}
                                {(onEdit || onDelete || onShow) && (
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            {onShow && (
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => onShow(row)}
                                                    className="h-8 w-8"
                                                >
                                                    <Eye className="h-4 w-4" />
                                                    <span className="sr-only">Voir détails</span>
                                                </Button>
                                            )}
                                            {onEdit && (
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => onEdit(row)}
                                                    className="h-8 w-8"
                                                >
                                                    <Pencil className="h-4 w-4" />
                                                    <span className="sr-only">Modifier</span>
                                                </Button>
                                            )}
                                            {onDelete && (
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => onDelete(row)}
                                                    className="h-8 w-8 text-destructive hover:text-destructive"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                    <span className="sr-only">Supprimer</span>
                                                </Button>
                                            )}
                                        </div>
                                    </TableCell>
                                )}
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
                {pagination && (
                    <Pagination
                        links={pagination.links}
                        from={pagination.from}
                        to={pagination.to}
                        total={pagination.total}
                    />
                )}
            </CardContent>
        </Card>
    );
}


