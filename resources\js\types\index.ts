import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    type?: string | null;
    title: string;
    href?: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
    children?: NavItem[];
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    flash: {
        success?: string | null;
        error?: string | null;
        warning?: string | null;
        info?: string | null;
    };
    ziggy: Config & { location: string };
    [key: string]: unknown;
}

export interface User {
    id: number;
    nom: string;
    prenom: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    role: 'admin' | 'client';
    civilite?: string;
    date_naissance?: string;
    lieu_naissance?: string;
    ville?: string;
    code_postal?: string;
    adresse?: string;
    mobile?: string;
    tel?: string;
    num_permis?: string;
    date_permis?: string;
    lieu_permis?: string;
    reservations?: Reservation[];
    reservations_tests_psychos?: ReservationTestPsycho[];
    [key: string]: unknown; // This allows for additional properties...
}

export interface Departement {
    id: number;
    code: string;
    nom: string;
}

export interface Ville {
    id: number;
    nom: string;
    code?: string;
    reg_code?: string;
    dep_code?: string;
    code_postal?: string;
    departement_id: number;
    departement?: Departement;
    lieus?: Lieu[];
}

export interface Lieu {
    id: number;
    nom: string;
    adresse: string;
    ville_id: number;
    departement_id?: number;
    ville?: {
        id: number;
        nom: string;
        departement_id?: number;
        departement?: {
            id: number;
            nom: string;
            code: string;
        };
    };
    stages?: {
        id: number;
        date_debut: string;
        date_fin: string;
        places_disponibles: number;
        prix: number;
        reference: string;
    }[];
}

export interface Stage {
    id: number;
    date_debut: string;
    date_fin: string;
    lieu_id: number;
    places_disponibles: number;
    prix: number;
    reference: string;
    lieu?: Lieu;
    reservations: Reservation[];
}

export interface Reservation {
    id: number;
    stage_id: number;
    user_id: number;
    type_stage_id: number;
    date_reservation: string;
    statut: 'confirmée' | 'en attente' | 'annulée';
    date_infraction?: string;
    heure_infraction?: string;
    lieu_infraction?: string;
    permis_recto?: string;
    permis_verso?: string;
    lettre_48n_recto?: string;
    lettre_48n_verso?: string;
    cas?: string;
    date_paiement?: string;
    methode_paiement?: string;
    transaction_id?: string;
    stage?: Stage;
    user?: User;
    type_stage?: TypeStage;
}

export interface TypeStage {
    id: number;
    nom: string;
    description: string | null;
    reservations_count?: number;
    reservations?: Reservation[];
}

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    auth: {
        user: User;
    };
};

export interface PaginatedData<T> {
  data: T[];
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: {
    url: string | null;
    label: string;
    active: boolean;
  }[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface StagesProps {
  stages: PaginatedData<Stage>;
  departements: Departement[];
  filters: {
    departement?: string;
    mois?: string;
  };
}

export interface AdminStagesProps extends PageProps {
  stages: PaginatedData<Stage>;
  lieux: Lieu[];
  departements: Departement[];
  villes: Ville[];
  filters: {
    search?: string;
    departement?: string;
    ville?: string;
    date_debut?: string;
    date_fin?: string;
    prix_min?: string;
    prix_max?: string;
  };
  isArchive: boolean;
}

export interface AdminTestsProps extends PageProps {
  tests: PaginatedData<TestPsycho>;
  lieux: Lieu[];
  departements: Departement[];
  villes: Ville[];
  filters: {
    search?: string;
    departement?: string;
    ville?: string;
    date_debut?: string;
    date_fin?: string;
    prix_min?: string;
    prix_max?: string;
  };
  isArchive: boolean;
}

export interface AdminReservationsProps extends PageProps {
  reservations: PaginatedData<Reservation>;
  stages: Stage[];
  typeStages: TypeStage[];
  users: User[];
  departements: Departement[];
  villes: Ville[];
  filters: {
    search?: string;
    statut?: string;
    type_stage?: string;
    date_debut?: string;
    date_fin?: string;
    customer_search?: string;
    methode_paiement?: string;
    departement?: string;
    ville?: string;
  };
  isArchive: boolean;
}

export interface TestPsycho {
    id: number;
    date: string;
    lieu_id: number;
    places_disponibles: number;
    prix: number;
    reference: string;
    lieu?: Lieu;
    reservations_count?: number;
    reservations?: ReservationTestPsycho[];
}

export interface TypeTestPsycho {
    id: number;
    nom: string;
    description: string | null;
    reservations_count?: number;
    reservations?: ReservationTestPsycho[];
}

export interface ReservationTestPsycho {
    id: number;
    test_psycho_id: number;
    user_id: number;
    type_test_psycho_id: number;
    date_reservation: string;
    statut: 'confirmée' | 'en attente' | 'annulée';
    motif?: string;
    permis_recto?: string;
    permis_verso?: string;
    document_tribunal?: string;
    date_paiement?: string;
    methode_paiement?: string;
    transaction_id?: string;
    test_psycho?: TestPsycho;
    user?: User;
    type_test_psycho?: TypeTestPsycho;
}


