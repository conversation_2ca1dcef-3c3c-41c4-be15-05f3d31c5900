import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { PasswordInput } from '@/components/password-input';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { Card, CardContent } from '@/components/ui/card';

type LoginForm = {
  email: string;
  password: string;
  remember: boolean;
};

interface LoginProps {
  status?: string;
  canResetPassword: boolean;
}

export default function Login({ status, canResetPassword }: LoginProps) {
  const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
    email: '',
    password: '',
    remember: false,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();
    post(route('login'), {
      onFinish: () => reset('password'),
    });
  };

  return (
    <AuthLayout title="Connectez-vous à votre compte" description="Entrez votre email et mot de passe ci-dessous pour vous connecter">
      <Card className="rounded-xl border shadow-sm">
        <CardContent className="px-6 py-8">
          <form className="flex flex-col gap-6" onSubmit={submit}>
            <div className="grid gap-6">
              <div className="grid gap-2">
                <Label htmlFor="email">Adresse email</Label>
                <Input
                  id="email"
                  type="email"
                  required
                  autoFocus
                  tabIndex={1}
                  autoComplete="email"
                  value={data.email}
                  onChange={(e) => setData('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
                <InputError message={errors.email} />
              </div>

              <div className="grid gap-2">
                <div className="flex items-center">
                  <Label htmlFor="password">Mot de passe</Label>
                  {canResetPassword && (
                    <TextLink href={route('password.request')} className="ml-auto text-sm" tabIndex={5}>
                      Mot de passe oublié ?
                    </TextLink>
                  )}
                </div>
                <PasswordInput
                  id="password"
                  required
                  tabIndex={2}
                  autoComplete="current-password"
                  value={data.password}
                  onChange={(e) => setData('password', e.target.value)}
                  placeholder="Mot de passe"
                />
                <InputError message={errors.password} />
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox id="remember" name="remember" checked={data.remember} onClick={() => setData('remember', !data.remember)} tabIndex={3} />
                <Label htmlFor="remember">Se souvenir de moi</Label>
              </div>

              <Button type="submit" className="mt-4 w-full" tabIndex={4} disabled={processing}>
                {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                Se connecter
              </Button>
            </div>

            <div className="text-center">
              <p className="text-muted-foreground text-sm mb-2">
                Vous n'avez pas de compte ?
              </p>
              <TextLink
                href={route('register')}
                tabIndex={5}
                className="text-lg font-semibold text-primary hover:text-primary/80 underline decoration-2 underline-offset-4"
              >
                Cliquez ici pour vous inscrire
              </TextLink>
            </div>
          </form>

          {status && <div className="mb-4 text-center text-sm font-medium text-green-600">{status}</div>}
        </CardContent>
      </Card>
      <Head title="Connexion" />
    </AuthLayout>
  );
}
