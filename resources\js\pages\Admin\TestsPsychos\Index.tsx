import { Head, router } from '@inertiajs/react';
import { AdminTestsProps, TestPsycho, BreadcrumbItem } from '@/types';
import TabNavigation from '@/components/TabNavigation';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { DataTableColumnHeader } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import AdminFilters from '@/components/AdminFilters';
import { Pencil, Trash2 } from 'lucide-react';

export default function Index({ tests, lieux, departements, villes, filters, isArchive = false }: AdminTestsProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isImportOpen, setIsImportOpen] = useState(false);
    const [editingTest, setEditingTest] = useState<TestPsycho | null>(null);

    const form = useForm({
        date: '',
        lieu_id: '',
        places_disponibles: '',
        prix: '',
        reference: '',
    });

    const importForm = useForm({
        file: null as File | null,
    });

    const columns: ColumnDef<TestPsycho>[] = [
        {
            accessorKey: "reference",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Référence" />
            ),
            enableSorting: true,
            enableHiding: false,
        },
        {
            accessorKey: "date",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Date" />
            ),
            cell: ({ row }) => {
                const date = row.getValue("date") as string;
                return format(parseISO(date), 'dd/MM/yyyy', { locale: fr });
            },
            enableSorting: true,
        },
        {
            id: "lieu",
            header: "Lieu",
            cell: ({ row }) => {
                const test = row.original;
                return (
                    <div className="flex flex-col">
                        <span className="font-medium">{test.lieu?.nom}</span>
                        <span className="text-sm text-muted-foreground">
                            {test.lieu?.ville?.nom} ({test.lieu?.ville?.departement?.code})
                        </span>
                    </div>
                );
            },
            enableSorting: false,
        },
        {
            accessorKey: "prix",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Prix" />
            ),
            cell: ({ row }) => {
                const prix = row.getValue("prix") as number;
                return `${prix} €`;
            },
            enableSorting: true,
        },
        {
            accessorKey: "reservations_count",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title="Réservations" />
            ),
            cell: ({ row }) => {
                const test = row.original;
                const count = row.getValue("reservations_count") as number;

                if (count === 0) {
                    return <span className="text-muted-foreground">0</span>;
                }

                return (
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 hover:text-blue-800 font-medium"
                        onClick={() => router.visit(route('admin.reservations-tests-psychos.by-test', test.id))}
                    >
                        {count}
                    </Button>
                );
            },
            enableSorting: true,
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => {
                const test = row.original;
                return (
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEdit(test)}
                            className="h-8 w-8"
                        >
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Modifier</span>
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(test)}
                            className="h-8 w-8 text-destructive hover:text-destructive"
                        >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Supprimer</span>
                        </Button>
                    </div>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
    ];

    const handleAdd = () => {
        setEditingTest(null);
        form.reset();
        setIsOpen(true);
    };

    const handleImport = () => {
        importForm.reset();
        setIsImportOpen(true);
    };

    const handleImportSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        importForm.post(route('admin.tests-psychos.import'), {
            forceFormData: true,
            onSuccess: () => {
                setIsImportOpen(false);
                // toast.success('Tests psychotechniques importés avec succès');
            },
            onError: (errors) => {
                console.log(errors);
                toast.error('Erreur lors de l\'importation : ' + (errors.file || errors.message || 'Erreur inconnue'));
            }
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            importForm.setData('file', e.target.files[0]);
        }
    };

    const handleEdit = (test: TestPsycho) => {
        setEditingTest(test);
        console.log('test data:', test);

        // Vérifier que les propriétés existent avant d'appeler toString()
        const lieu_id = test.lieu?.id !== undefined ? test.lieu.id.toString() : '';
        const places_disponibles = test.places_disponibles !== undefined ? test.places_disponibles.toString() : '';
        const prix = test.prix !== undefined ? test.prix.toString() : '';

        // Formater la date pour l'input HTML date (yyyy-mm-dd)
        const formattedDate = test.date ? format(parseISO(test.date), 'yyyy-MM-dd') : '';

        form.setData({
            date: formattedDate,
            lieu_id: lieu_id,
            places_disponibles: places_disponibles,
            prix: prix,
            reference: test.reference || '',
        });
        setIsOpen(true);
    };

    const handleDelete = (test: TestPsycho) => {
        if (confirm(`Êtes-vous sûr de vouloir supprimer le test psychotechnique "${test.reference}" ?`)) {
            router.delete(`/admin/tests-psychos/${test.id}`);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingTest) {
            form.put(`/admin/tests-psychos/${editingTest.id}`, {
                onSuccess: () => setIsOpen(false),
            });
        } else {
            form.post('/admin/tests-psychos', {
                onSuccess: () => setIsOpen(false),
            });
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Tests psychotechniques',
            href: '/admin/tests-psychos',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Tests psychotechniques" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <TabNavigation
                    tabs={[
                        {
                            name: 'Tests à venir',
                            href: route('admin.tests-psychos.index'),
                            current: !isArchive,
                        },
                        {
                            name: 'Archives',
                            href: route('admin.tests-psychos.archive'),
                            current: isArchive,
                        },
                    ]}
                />
                <AdminFilters
                    filters={filters}
                    departements={departements}
                    villes={villes}
                    routeName={isArchive ? 'admin.tests-psychos.archive' : 'admin.tests-psychos.index'}
                    isArchive={isArchive}
                />

                <DataTable
                    title={isArchive ? "Archives des tests psychotechniques" : "Tests psychotechniques"}
                    columns={columns}
                    data={tests.data}
                    onAdd={!isArchive ? handleAdd : undefined}
                    onImport={!isArchive ? handleImport : undefined}
                    pagination={{
                        links: tests.links,
                        from: tests.from,
                        to: tests.to,
                        total: tests.total
                    }}
                />
            </div>

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[900px]">
                    <DialogHeader>
                        <DialogTitle>
                            {editingTest ? 'Modifier le test psychotechnique' : 'Ajouter un test psychotechnique'}
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <label htmlFor="reference" className="text-sm font-medium">Référence</label>
                            <Input
                                id="reference"
                                value={form.data.reference}
                                onChange={e => form.setData('reference', e.target.value)}
                                required
                            />
                            {form.errors.reference && <p className="text-sm text-red-500">{form.errors.reference}</p>}
                        </div>
                        <div className="space-y-2">
                            <label htmlFor="date" className="text-sm font-medium">Date</label>
                            <Input
                                id="date"
                                type="date"
                                value={form.data.date}
                                onChange={e => form.setData('date', e.target.value)}
                                required
                            />
                            {form.errors.date && <p className="text-sm text-red-500">{form.errors.date}</p>}
                        </div>

                        <div className="space-y-2">
                            <label htmlFor="lieu_id" className="text-sm font-medium">Lieu</label>
                            <Select
                                value={form.data.lieu_id}
                                onValueChange={value => form.setData('lieu_id', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Sélectionner un lieu" />
                                </SelectTrigger>
                                <SelectContent>
                                    {lieux.map(lieu => (
                                        <SelectItem key={lieu.id} value={lieu.id.toString()}>
                                            {lieu.nom} ({lieu.ville?.nom})
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {form.errors.lieu_id && <p className="text-sm text-red-500">{form.errors.lieu_id}</p>}
                        </div>
                        <div className="space-y-2">
                            <label htmlFor="places_disponibles" className="text-sm font-medium">Places disponibles</label>
                            <Input
                                id="places_disponibles"
                                type="number"
                                min="1"
                                value={form.data.places_disponibles}
                                onChange={e => form.setData('places_disponibles', e.target.value)}
                                required
                            />
                            {form.errors.places_disponibles && <p className="text-sm text-red-500">{form.errors.places_disponibles}</p>}
                        </div>
                        <div className="space-y-2">
                            <label htmlFor="prix" className="text-sm font-medium">Prix (€)</label>
                            <Input
                                id="prix"
                                type="number"
                                min="0"
                                step="0.01"
                                value={form.data.prix}
                                onChange={e => form.setData('prix', e.target.value)}
                                required
                            />
                            {form.errors.prix && <p className="text-sm text-red-500">{form.errors.prix}</p>}
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingTest ? 'Mettre à jour' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            Importer des tests psychotechniques
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleImportSubmit} className="space-y-4">
                        <div>
                            <Label htmlFor="file">Fichier Excel</Label>
                            <Input
                                id="file"
                                type="file"
                                accept=".xlsx,.xls"
                                onChange={handleFileChange}
                                className="mt-1"
                            />
                            {importForm.errors.file && (
                                <p className="text-sm text-red-500 mt-1">{importForm.errors.file}</p>
                            )}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                            <p>Le fichier doit contenir les colonnes suivantes :</p>
                            <ul className="list-disc pl-5 mt-2">
                                <li>date (format: DD/MM/YYYY)</li>
                                <li>prix</li>
                                <li>places_disponibles</li>
                                <li>lieu_id</li>
                            </ul>
                            <p className="mt-2">
                                Veuillez utiliser un fichier Excel (.xlsx ou .xls) avec les colonnes indiquées ci-dessus.
                            </p>
                            <p className="mt-2">
                                <strong>Note:</strong> Les dates peuvent être au format texte (JJ/MM/AAAA) ou au format numérique Excel.
                            </p>
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsImportOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={importForm.processing}>
                                Importer
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}





