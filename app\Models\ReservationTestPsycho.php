<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReservationTestPsycho extends Model
{
    use HasFactory;

    protected $fillable = [
        'test_psycho_id',
        'user_id',
        'type_test_psycho_id',
        'date_reservation',
        'statut',
        'motif',
        'permis_recto',
        'permis_verso',
        'document_tribunal',
        'date_paiement',
        'methode_paiement',
        'transaction_id'
    ];

    protected $casts = [
        'date_reservation' => 'datetime',
        'date_paiement' => 'datetime'
    ];

    public function testPsycho()
    {
        return $this->belongsTo(TestPsycho::class, 'test_psycho_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function typeTestPsycho()
    {
        return $this->belongsTo(TypeTestPsycho::class, 'type_test_psycho_id');
    }
}
