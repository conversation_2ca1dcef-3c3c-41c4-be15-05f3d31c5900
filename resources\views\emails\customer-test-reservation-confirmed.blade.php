@extends('emails.layout')

@section('title', 'Confirmation de votre réservation - Test psychotechnique')

@section('header-title', 'Réservation Confirmée')
@section('header-subtitle', 'Votre test psychotechnique est confirmé')

@section('content')
    <div class="greeting">
        Bonjour {{ $user->prenom }} {{ $user->nom }},
    </div>
    
    <p style="font-size: 16px; margin-bottom: 25px; color: #495057;">
        Nous avons le plaisir de vous confirmer votre réservation pour le test psychotechnique. 
        Voici les détails de votre réservation :
    </p>
    
    <div class="highlight-box">
        <div class="amount">Réservation #{{ $reservation->id }}</div>
        <div>{{ $typeTest->nom ?? 'Test psychotechnique' }}</div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📋 Détails de votre réservation</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Numéro de réservation :</span>
                <span class="info-value"><strong>#{{ $reservation->id }}</strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Type de test :</span>
                <span class="info-value">{{ $typeTest->nom ?? 'Non spécifié' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Date de réservation :</span>
                <span class="info-value">{{ $reservation->date_reservation->format('d/m/Y à H:i') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Statut :</span>
                <span class="info-value">
                    <span class="status-badge status-{{ strtolower($reservation->statut) }}">
                        {{ ucfirst($reservation->statut) }}
                    </span>
                </span>
            </div>
            @if($reservation->methode_paiement)
            <div class="info-row">
                <span class="info-label">Méthode de paiement :</span>
                <span class="info-value">{{ ucfirst(str_replace('_', ' ', $reservation->methode_paiement)) }}</span>
            </div>
            @endif
            @if($reservation->date_paiement)
            <div class="info-row">
                <span class="info-label">Date de paiement :</span>
                <span class="info-value">{{ $reservation->date_paiement->format('d/m/Y à H:i') }}</span>
            </div>
            @endif
            @if($reservation->motif)
            <div class="info-row">
                <span class="info-label">Motif :</span>
                <span class="info-value">{{ $reservation->motif }}</span>
            </div>
            @endif
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📅 Informations sur le test</h2>
        <div class="info-grid">
            @if($testPsycho)
                <div class="info-row">
                    <span class="info-label">Date du test :</span>
                    <span class="info-value"><strong>{{ $testPsycho->date->format('d/m/Y') }}</strong></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Prix :</span>
                    <span class="info-value"><strong>{{ number_format($testPsycho->prix, 2, ',', ' ') }} €</strong></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Référence :</span>
                    <span class="info-value">{{ $testPsycho->reference }}</span>
                </div>
                @if($testPsycho->lieu)
                <div class="info-row">
                    <span class="info-label">Lieu :</span>
                    <span class="info-value">{{ $testPsycho->lieu->nom }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Adresse :</span>
                    <span class="info-value">{{ $testPsycho->lieu->adresse }}</span>
                </div>
                @if($testPsycho->lieu->ville)
                <div class="info-row">
                    <span class="info-label">Ville :</span>
                    <span class="info-value">{{ $testPsycho->lieu->ville->nom }} ({{ $testPsycho->lieu->ville->code_postal }})</span>
                </div>
                @endif
                @endif
            @endif
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">👤 Vos informations</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Nom :</span>
                <span class="info-value">{{ $user->nom }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Prénom :</span>
                <span class="info-value">{{ $user->prenom }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Email :</span>
                <span class="info-value">{{ $user->email }}</span>
            </div>
            @if($user->telephone)
            <div class="info-row">
                <span class="info-label">Téléphone :</span>
                <span class="info-value">{{ $user->telephone }}</span>
            </div>
            @endif
        </div>
    </div>
    
    <div class="divider"></div>
    
    <div style="background-color: #e8f5e8; border-left: 4px solid #4caf50; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
        <h3 style="color: #2e7d32; margin-bottom: 10px;">📝 Informations importantes</h3>
        <ul style="margin: 0; padding-left: 20px; color: #495057;">
            <li style="margin-bottom: 8px;">Veuillez vous présenter 15 minutes avant le début du test</li>
            <li style="margin-bottom: 8px;">Munissez-vous d'une pièce d'identité en cours de validité</li>
            <li style="margin-bottom: 8px;">Apportez votre permis de conduire (si applicable)</li>
            <li style="margin-bottom: 8px;">Le test dure environ 45 minutes</li>
            <li style="margin-bottom: 8px;">En cas d'empêchement, contactez-nous au plus tôt</li>
        </ul>
    </div>
    
    <p style="font-size: 16px; color: #495057; text-align: center; margin-top: 30px;">
        Nous vous remercions de votre confiance et vous souhaitons un excellent test !
    </p>
    
    <p style="font-size: 14px; color: #6c757d; text-align: center; margin-top: 20px;">
        Si vous avez des questions, n'hésitez pas à nous contacter.
    </p>
@endsection
